substitutions:
    uv_ao_pin: GPIO35
    uv_update_interval: '5s'
    uv_voltage_offset: 140.0

globals:
    - id: uv_voltage_offset
      type: float
      restore_value: false
      initial_value: $uv_voltage_offset

sensor:
    - platform: adc
      id: uv_sensor_voltage
      name: 'UV Sensor Voltage'
      icon: 'mdi:weather-sunny-alert'
      unit_of_measurement: 'mV'
      device_class: voltage
      accuracy_decimals: 0
      entity_category: diagnostic
      update_interval: $uv_update_interval
      pin: $uv_ao_pin
      attenuation: 2.5db
      filters:
          - multiply: 1000.0

    - platform: copy
      source_id: uv_sensor_voltage
      id: uv_index
      name: 'UV Index'
      icon: 'mdi:white-balance-sunny'
      accuracy_decimals: 0
      unit_of_measurement: ''
      device_class: ''
      lambda: |-
          float voltage = id(uv_sensor_voltage).state - $uv_voltage_offset;
          float uv_index = 0.0;

          if (voltage >= 140.0) {
              uv_index = (voltage - 140.0) / 87.0;
          }

          return std::max(0.0, std::min(uv_index, 11.0));
